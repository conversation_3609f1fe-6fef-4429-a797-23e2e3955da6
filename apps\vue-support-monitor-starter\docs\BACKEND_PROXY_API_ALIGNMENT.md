# 后端代理接口对齐文档

## 修改概述

本次修改主要是根据后端 `MonitorSysGenServerProxy` 实体类的实际字段，调整前端页面和接口，确保前后端数据结构完全对齐。

## 后端实体分析

### MonitorSysGenServerProxy 实体字段

根据后端代码分析，`MonitorSysGenServerProxy` 实体包含以下字段：

```java
public class MonitorSysGenServerProxy extends SysBase {
    private Integer monitorSysGenServerProxyId;           // 服务器代理关联ID
    private Integer monitorSysGenServerId;                // 服务器ID
    private String monitorSysGenServerProxyType;          // 代理类型 (GUACAMOLE, VNC, RDP等)
    private String monitorSysGenServerProxyConfig;        // 代理配置 (JSON格式)
    private Integer monitorSysGenServerProxyEnabled;      // 是否启用 0:否 1:是
    private Integer monitorSysGenServerProxyStatus;       // 代理状态 0:离线 1:在线 2:连接中 3:连接失败
    private String monitorSysGenServerProxyLastConnectTime; // 最后连接时间
    private String monitorSysGenServerProxyConnectionError; // 连接失败原因
    private String monitorSysGenServerProxyUrl;           // 代理URL
    private String monitorSysGenServerProxyCreateTime;    // 创建时间
    private String monitorSysGenServerProxyUpdateTime;    // 更新时间
    private String monitorSysGenServerProxyRemark;        // 备注
}
```

### 后端控制器接口

```java
@RestController
@RequestMapping("/v1/gen/server/proxy")
public class MonitorSysGenServerProxyController {
    
    @GetMapping("/page")
    public ReturnResult<PageResult<MonitorSysGenServerProxy>> page(ProxyPageRequest request);
    
    @GetMapping("/server/{serverId}")
    public ReturnResult<List<MonitorSysGenServerProxy>> getByServerId(@PathVariable Integer serverId);
    
    @GetMapping("/proxy/{proxyId}")
    public ReturnResult<List<MonitorSysGenServerProxy>> getByProxyId(@PathVariable Integer proxyId);
    
    @PostMapping("/save")
    public ReturnResult<MonitorSysGenServerProxy> save(@RequestBody MonitorSysGenServerProxy proxy);
    
    @PutMapping("/update")
    public ReturnResult<Boolean> update(@RequestBody MonitorSysGenServerProxy proxy);
    
    @DeleteMapping("/delete/{id}")
    public ReturnResult<Boolean> delete(@PathVariable Integer id);
    
    @PostMapping("/enable/{id}")
    public ReturnResult<Boolean> enable(@PathVariable Integer id);
    
    @PostMapping("/disable/{id}")
    public ReturnResult<Boolean> disable(@PathVariable Integer id);
    
    @PostMapping("/test/{id}")
    public ReturnResult<Boolean> test(@PathVariable Integer id);
    
    @GetMapping("/url/{id}")
    public ReturnResult<String> getUrl(@PathVariable Integer id);
}
```

## 前端修改内容

### 1. 更新 ServerProxy 接口定义

**文件**: `src/api/server/proxy.ts`

```typescript
export interface ServerProxy {
  monitorSysGenServerProxyId?: number;           // 服务器代理关联ID
  monitorSysGenServerId?: number;                // 服务器ID
  monitorSysGenServerProxyType?: string;         // 代理类型
  monitorSysGenServerProxyConfig?: string;       // 代理配置
  monitorSysGenServerProxyEnabled?: number;      // 是否启用
  monitorSysGenServerProxyStatus?: number;       // 代理状态
  monitorSysGenServerProxyLastConnectTime?: string; // 最后连接时间
  monitorSysGenServerProxyConnectionError?: string; // 连接失败原因
  monitorSysGenServerProxyUrl?: string;          // 代理URL
  monitorSysGenServerProxyCreateTime?: string;   // 创建时间
  monitorSysGenServerProxyUpdateTime?: string;   // 更新时间
  monitorSysGenServerProxyRemark?: string;       // 备注
  // 继承自SysBase的字段
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}
```

### 2. 更新API函数

**新增的API函数**：

```typescript
// 基础CRUD操作
export function getServerProxyPageList(params: ProxyPageRequest)
export function getServerProxyList(serverId: number)
export function createServerProxy(serverProxy: ServerProxy)
export function updateServerProxy(serverProxy: ServerProxy)
export function deleteServerProxy(id: number)

// 状态管理
export function enableServerProxy(id: number)
export function disableServerProxy(id: number)
export function testServerProxyConnection(id: number)

// 批量操作
export function batchDeleteServerProxy(ids: number[])
export function batchUpdateServerProxyStatus(ids: number[], status: number)

// 高级功能
export function getServerProxyUrl(id: number)
export function getServerProxyStatistics()
export function cloneServerProxyConfig(sourceServerId: number, targetServerId: number)
```

### 3. 更新 ServerEditDialog.vue

**主要修改**：

1. **导入路径修正**：
   ```typescript
   // 修改前
   import { type MonitorProxy, getProxyPageList } from "@/api/monitor/gen/proxy";
   
   // 修改后
   import { type ServerProxy, getServerProxyList } from "@/api/server/proxy";
   ```

2. **字段映射修正**：
   ```typescript
   // 代理选项显示
   :label="`${proxy.monitorSysGenServerProxyType} 代理`"
   :value="proxy.monitorSysGenServerProxyId"
   
   // 代理信息显示
   {{ proxy.monitorSysGenServerProxyUrl || '未配置URL' }}
   {{ proxy.monitorSysGenServerProxyRemark }}
   ```

3. **状态处理增强**：
   ```typescript
   const getProxyStatusText = (status: number) => {
     switch (status) {
       case 0: return '离线';
       case 1: return '在线';
       case 2: return '连接中';
       case 3: return '连接失败';
       default: return '未知';
     }
   };
   ```

4. **代理类型图标扩展**：
   ```typescript
   const getProxyTypeIcon = (proxyType: string) => {
     switch (proxyType) {
       case 'GUACAMOLE': return 'ri:remote-control-line';
       case 'VNC': return 'ri:computer-line';
       case 'RDP': return 'ri:windows-line';
       // ... 其他类型
     }
   };
   ```

### 4. 更新代理管理页面

**文件**: `src/views/server/modules/proxy-management/index.vue`

1. **API导入更新**：
   ```typescript
   import {
     getServerProxyPageList,
     enableServerProxy,
     disableServerProxy,
     deleteServerProxy,
     testServerProxyConnection,
     // ... 其他API
   } from "@/api/server/proxy";
   ```

2. **数据类型更新**：
   ```typescript
   const tableData = ref<ServerProxy[]>([]);
   const selectedRows = ref<ServerProxy[]>([]);
   ```

3. **搜索参数调整**：
   ```typescript
   const searchForm = reactive({
     keyword: "",
     proxyType: "",
     status: undefined as number | undefined,
     enabled: undefined as number | undefined
   });
   ```

## 数据流程说明

### 1. 服务器代理关联流程

1. **创建关联**：服务器创建时可以配置代理
2. **查询关联**：根据服务器ID查询其代理配置
3. **管理关联**：启用/禁用、测试连接、更新配置

### 2. 代理状态管理

- **0**: 离线 - 代理服务不可用
- **1**: 在线 - 代理服务正常
- **2**: 连接中 - 正在建立连接
- **3**: 连接失败 - 连接建立失败

### 3. 代理类型支持

- **GUACAMOLE**: Apache Guacamole 远程桌面代理
- **VNC**: VNC 协议代理
- **RDP**: RDP 协议代理
- **HTTP**: HTTP 代理
- **SOCKS4/SOCKS5**: SOCKS 代理

## 路由配置

在 `monitor-system.ts` 中添加了代理管理路由：

```typescript
{
  path: "/server/proxy",
  name: "serverProxy",
  component: () => import("@/views/server/modules/proxy-management/index.vue"),
  meta: {
    icon: "ri:router-line",
    title: "代理管理",
    showLink: true,
    showParent: true
  }
}
```

## 验证清单

- [x] 后端实体字段分析完成
- [x] 前端接口定义更新
- [x] API函数对齐后端接口
- [x] ServerEditDialog 字段映射修正
- [x] 代理管理页面更新
- [x] 路由配置添加
- [x] 状态处理逻辑完善
- [x] 错误处理机制优化

## 注意事项

1. **字段命名规范**：严格按照后端实体字段命名
2. **状态码对应**：确保前端状态显示与后端状态码一致
3. **API路径规范**：使用 `/v1/gen/server/proxy` 前缀
4. **错误处理**：统一使用 `ReturnResult` 响应格式
5. **类型安全**：所有接口都有完整的TypeScript类型定义

## 后续优化建议

1. **缓存机制**：添加代理列表缓存，减少重复请求
2. **实时状态**：考虑使用WebSocket实时更新代理状态
3. **批量操作**：增强批量管理功能
4. **配置模板**：提供常用代理配置模板
5. **监控告警**：添加代理连接状态监控和告警
