///
/// Copyright (c) 2019 Of Him Code Technology Studio
/// Jpom is licensed under Mulan PSL v2.
/// You can use this software according to the terms and conditions of the Mulan PSL v2.
/// You may obtain a copy of Mulan PSL v2 at:
/// 			http://license.coscl.org.cn/MulanPSL2
/// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
/// See the Mulan PSL v2 for more details.
///

import { http, type ReturnResult } from "@repo/utils";

// ==================== 类型定义 ====================

/**
 * 服务器代理接口
 */
export interface ServerProxy {
  monitorSysGenServerProxyId: number | null;
  monitorSysGenServerProxyName: string;
  monitorSysGenServerProxyType: string;
  monitorSysGenServerProxyHost: string;
  monitorSysGenServerProxyPort: number;
  monitorSysGenServerProxyUsername?: string;
  monitorSysGenServerProxyPassword?: string;
  monitorSysGenServerProxyStatus: number;
  monitorSysGenServerProxyDescription?: string;
  monitorSysGenServerProxyTimeout: number;
  monitorSysGenServerProxyAuthRequired: number;
  monitorSysGenServerProxyTags?: string;
  monitorSysGenServerProxyCreateTime?: string;
  monitorSysGenServerProxyUpdateTime?: string;
}

/**
 * 服务器代理分页查询参数
 */
export interface ServerProxyPageParams {
  page?: number;
  pageSize?: number;
  monitorSysGenServerProxyName?: string;
  monitorSysGenServerProxyType?: string;
  monitorSysGenServerProxyStatus?: number;
  monitorSysGenServerProxyTags?: string;
}

/**
 * 服务器代理保存参数
 */
export interface ServerProxySaveParams {
  monitorSysGenServerProxyId?: number | null;
  monitorSysGenServerProxyName: string;
  monitorSysGenServerProxyType: string;
  monitorSysGenServerProxyHost: string;
  monitorSysGenServerProxyPort: number;
  monitorSysGenServerProxyUsername?: string;
  monitorSysGenServerProxyPassword?: string;
  monitorSysGenServerProxyStatus: number;
  monitorSysGenServerProxyDescription?: string;
  monitorSysGenServerProxyTimeout: number;
  monitorSysGenServerProxyAuthRequired: number;
  monitorSysGenServerProxyTags?: string;
}

/**
 * 代理连接测试结果
 */
export interface ProxyTestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  errorDetails?: string;
}

// ==================== API 函数 ====================

/**
 * 服务器代理管理 API
 */

/**
 * 分页查询服务器代理列表
 * @param params 查询参数
 * @returns 服务器代理分页数据
 */
export function getServerProxyPageList(params: ServerProxyPageParams) {
  return http.request<ReturnResult<{ records: ServerProxy[]; total: number }>>(
    "get",
    "v1/gen/server-proxy/page",
    { params }
  );
}

/**
 * 获取服务器代理详情
 * @param id 代理ID
 * @returns 代理详细信息
 */
export function getServerProxyDetail(id: number) {
  return http.request<ReturnResult<ServerProxy>>(
    "get",
    "v1/gen/server-proxy/get",
    { params: { id: id.toString() } }
  );
}

/**
 * 获取所有服务器代理列表（用于下拉选择）
 * @returns 代理列表
 */
export function getAllServerProxyList() {
  return http.request<ReturnResult<ServerProxy[]>>(
    "get",
    "v1/gen/server-proxy/list"
  );
}

/**
 * 保存服务器代理
 * @param data 代理数据
 * @returns 保存结果
 */
export function saveProxy(data: ServerProxySaveParams) {
  return http.request<ReturnResult<ServerProxy>>(
    "post",
    "v1/gen/server-proxy/save",
    { data }
  );
}

/**
 * 更新服务器代理
 * @param data 代理数据
 * @returns 更新结果
 */
export function updateProxy(data: ServerProxySaveParams) {
  return http.request<ReturnResult<boolean>>(
    "put",
    "v1/gen/server-proxy/update",
    { data }
  );
}

/**
 * 删除服务器代理
 * @param id 代理ID
 * @returns 删除结果
 */
export function deleteServerProxy(id: number) {
  return http.request<ReturnResult<boolean>>(
    "delete",
    "v1/gen/server-proxy/delete",
    { params: { id: id.toString() } }
  );
}

/**
 * 测试代理连接
 * @param id 代理ID
 * @returns 测试结果
 */
export function testProxyConnection(id: number) {
  return http.request<ReturnResult<ProxyTestResult>>(
    "get",
    "v1/gen/server-proxy/test",
    { params: { id: id.toString() } }
  );
}

/**
 * 启用/禁用服务器代理
 * @param id 代理ID
 * @param status 状态 (1: 启用, 0: 禁用)
 * @returns 操作结果
 */
export function toggleServerProxyStatus(id: number, status: number) {
  return http.request<ReturnResult<boolean>>(
    "put",
    "v1/gen/server-proxy/toggle-status",
    { data: { id, status } }
  );
}

/**
 * 批量删除服务器代理
 * @param ids 代理ID数组
 * @returns 删除结果
 */
export function batchDeleteServerProxy(ids: number[]) {
  return http.request<ReturnResult<boolean>>(
    "delete",
    "v1/gen/server-proxy/batch-delete",
    { data: { ids } }
  );
}

/**
 * 根据标签查询服务器代理
 * @param tags 标签数组
 * @returns 代理列表
 */
export function getServerProxyByTags(tags: string[]) {
  return http.request<ReturnResult<ServerProxy[]>>(
    "get",
    "v1/gen/server-proxy/by-tags",
    { params: { tags: tags.join(",") } }
  );
}

// ==================== 常量和枚举 ====================

/**
 * 服务器代理状态枚举
 */
export const SERVER_PROXY_STATUS = {
  DISABLED: 0,
  ENABLED: 1,
} as const;

export type ServerProxyStatus = typeof SERVER_PROXY_STATUS[keyof typeof SERVER_PROXY_STATUS];

/**
 * 服务器代理类型枚举
 */
export const SERVER_PROXY_TYPE = {
  HTTP: "HTTP",
  HTTPS: "HTTPS",
  SOCKS5: "SOCKS5",
  GUACAMOLE: "GUACAMOLE",
} as const;

export type ServerProxyType = typeof SERVER_PROXY_TYPE[keyof typeof SERVER_PROXY_TYPE];

/**
 * 认证要求枚举
 */
export const AUTH_REQUIRED = {
  NO: 0,
  YES: 1,
} as const;

export type AuthRequired = typeof AUTH_REQUIRED[keyof typeof AUTH_REQUIRED];

/**
 * 默认超时时间（毫秒）
 */
export const DEFAULT_TIMEOUT = 30000;

/**
 * 默认端口配置
 */
export const DEFAULT_PORTS = {
  HTTP: 8080,
  HTTPS: 8443,
  SOCKS5: 1080,
  GUACAMOLE: 4822,
} as const;
